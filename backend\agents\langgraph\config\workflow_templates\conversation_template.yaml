# Conversation Agent Workflow Template
# Optimized for ongoing multi-turn conversations between users and AI personas

template_id: "conversation_workflow"
template_name: "Conversation Agent Workflow"
version: "1.0.0"
description: "Optimized workflow template for ongoing conversations with AI personas, supporting multi-turn dialogues and context preservation"
agent_type: "conversation"

# Template metadata
metadata:
  author: "Datagenius Team"
  created_date: "2024-01-27"
  last_modified: "2024-01-27"
  tags: ["conversation", "dialogue", "multi-turn", "context-aware"]
  complexity: "medium"
  estimated_duration: "10-60 seconds per turn"

# Workflow configuration
workflow_config:
  # Execution settings - optimized for conversations
  max_execution_steps: 10  # Allow multiple conversation turns
  execution_timeout: 120   # 2 minutes max per conversation turn
  enable_parallel_execution: false  # Sequential for conversation flow

  # Loop prevention (conversation-aware)
  max_agent_executions: 5  # Allow multiple responses in conversation
  min_execution_interval: 1.0  # Normal interval for conversation flow
  enable_infinite_loop_detection: true
  rapid_execution_threshold: 3  # More lenient for conversations

  # Termination conditions (conversation-optimized)
  auto_terminate_on_response: false  # Don't terminate after single response
  require_explicit_continuation: false
  enable_workflow_completion_signals: true
  force_termination_on_agent_response: false  # Allow conversation to continue
  conversation_mode: true  # Enable conversation-specific behavior

# Node configuration
nodes:
  # Entry point - route to selected persona
  entry:
    type: "routing"
    config:
      enable_intelligent_routing: true
      fallback_to_concierge: false  # Don't fallback for conversations
      max_routing_attempts: 2
      conversation_aware: true
    
  # Agent execution node
  agent:
    type: "agent"
    config:
      max_retries: 2
      timeout: 30
      enable_fallback: true
      preserve_context: true  # Critical for conversations
      enable_memory: true
    
  # Tool execution (if needed)
  tools:
    type: "tool_execution"
    config:
      max_tool_calls: 3
      tool_timeout: 15
      enable_parallel_tools: false
      context_aware: true

  # Context management
  context:
    type: "context_manager"
    config:
      preserve_history: true
      max_history_length: 20  # Keep last 20 messages
      enable_summarization: true
      context_window: 4000  # Token limit for context

# Edge configuration (conversation flow patterns)
edges:
  # Start -> Routing (determine target agent)
  - from: "START"
    to: "routing"
    condition: "always"
    priority: 1
    
  # Routing -> Agent (execute selected persona)
  - from: "routing"
    to: "agent"
    condition: "agent_selected AND NOT workflow_complete"
    priority: 1
    
  # Agent -> Tools (if tools needed)
  - from: "agent"
    to: "tools"
    condition: "tools_required AND NOT workflow_complete"
    priority: 2
    
  # Tools -> Agent (return after tools)
  - from: "tools"
    to: "agent"
    condition: "tools_completed AND NOT workflow_complete"
    priority: 1
    
  # Agent -> Context (update conversation context)
  - from: "agent"
    to: "context"
    condition: "response_generated AND conversation_continues"
    priority: 3
    
  # Context -> END (conversation turn complete)
  - from: "context"
    to: "END"
    condition: "context_updated"
    priority: 1
    
  # Agent -> END (conversation complete)
  - from: "agent"
    to: "END"
    condition: "workflow_complete OR conversation_ended"
    priority: 1
    
  # Emergency fallback to END
  - from: "*"
    to: "END"
    condition: "error OR timeout OR max_executions_reached"
    priority: 10

# Termination conditions (conversation-aware)
termination_conditions:
  # Explicit conversation end
  - condition: "conversation_ended"
    action: "END"
    priority: 1
    
  # Workflow completion signal
  - condition: "workflow_complete"
    action: "END"
    priority: 1
    
  # User requests to end conversation
  - condition: "user_ends_conversation"
    action: "END"
    priority: 1
    
  # Error conditions
  - condition: "agent_error OR processing_error"
    action: "END"
    priority: 2
    
  # Timeout conditions
  - condition: "execution_timeout OR agent_timeout"
    action: "END"
    priority: 2
    
  # Loop prevention (more lenient)
  - condition: "infinite_loop_detected OR max_executions_reached"
    action: "END"
    priority: 3

# State management (conversation-optimized)
state_management:
  # Required state fields
  required_fields:
    - "user_id"
    - "conversation_id"
    - "messages"
    - "selected_agent"
    - "conversation_context"
    
  # State validation rules
  validation_rules:
    - field: "messages"
      rule: "not_empty"
      error_action: "continue_with_default"
      
    - field: "selected_agent"
      rule: "not_null"
      error_action: "route_to_fallback"
      
    - field: "conversation_context"
      rule: "preserve_history"
      error_action: "initialize_context"
  
  # State cleanup
  cleanup_on_completion: false  # Preserve conversation state
  preserve_conversation_history: true
  enable_context_compression: true

# Performance optimization (conversation-aware)
performance:
  # Caching
  enable_response_caching: false  # Disable for dynamic conversations
  enable_context_caching: true
  context_cache_duration: 1800  # 30 minutes
  
  # Resource limits
  memory_limit_mb: 100  # Higher for conversation context
  cpu_time_limit_seconds: 30
  
  # Monitoring
  enable_performance_tracking: true
  track_response_times: true
  track_conversation_metrics: true

# Error handling (conversation-friendly)
error_handling:
  # Retry configuration
  max_retries: 2
  retry_delay_seconds: 1.0
  exponential_backoff: true
  
  # Fallback strategies
  fallback_strategies:
    - trigger: "agent_unavailable"
      action: "use_fallback_agent"
      
    - trigger: "processing_error"
      action: "apologize_and_continue"
      
    - trigger: "timeout"
      action: "acknowledge_delay"
  
  # Error responses (conversation-appropriate)
  default_error_response: "I apologize, but I'm having trouble processing that. Could you please rephrase or try a different approach?"
  timeout_response: "I'm taking a bit longer than usual to respond. Let me try to help you with that..."
  agent_unavailable_response: "I'm temporarily unavailable, but I'm here to help. What would you like to discuss?"

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable: true
    required: false
    fallback_on_missing: true
    
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable: true  # Enable for rich conversations
    
  # MCP tools
  mcp_tools:
    enable: true
    max_tools: 5
    timeout: 20
    context_aware: true

# Conversation-specific settings
conversation:
  # Memory management
  enable_conversation_memory: true
  memory_window_size: 10  # Remember last 10 exchanges
  enable_long_term_memory: true
  
  # Context preservation
  preserve_user_preferences: true
  track_conversation_topics: true
  enable_topic_transitions: true
  
  # Response generation
  enable_contextual_responses: true
  maintain_personality_consistency: true
  adapt_to_user_style: true

# Monitoring and metrics
monitoring:
  # Metrics to collect
  metrics:
    - "response_time"
    - "conversation_length"
    - "user_satisfaction"
    - "context_retention"
    - "topic_coherence"
    - "agent_consistency"
    
  # Alerts
  alerts:
    - metric: "response_time"
      threshold: 15.0  # 15 seconds
      action: "log_performance_issue"
      
    - metric: "conversation_length"
      threshold: 50  # 50 turns
      action: "suggest_conversation_summary"

# Template validation
validation:
  # Required components
  required_components:
    - "routing_node"
    - "agent_node"
    - "context_manager"
    
  # Optional components
  optional_components:
    - "tool_execution_node"
    - "memory_manager"
    
  # Validation rules
  rules:
    - rule: "max_execution_steps >= 5"
      error: "Conversation workflows need multiple steps"
      
    - rule: "execution_timeout >= 60"
      error: "Conversations need adequate time per turn"

"""
Test conversation flow to ensure AI agents can have proper conversations with users.

This test verifies that the upgraded concierge template and workflow execution flow
support ongoing conversations rather than just single-shot interactions.
"""

import pytest
import asyncio
import uuid
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

from ..core.workflow_manager import WorkflowManager
from ..states.unified_state import create_unified_state, ConversationMode
from ..core.workflow_template_selector import WorkflowTemplateSelector


class TestConversationFlow:
    """Test the reconstructed conversation flow."""

    @pytest.fixture
    def workflow_manager(self):
        """Create a workflow manager for testing."""
        return WorkflowManager()

    @pytest.fixture
    def sample_conversation_state(self):
        """Create a sample conversation state."""
        return create_unified_state(
            user_id="test_user_123",
            conversation_id="test_conv_456",
            workflow_type="chat_message",
            initial_message={
                "id": str(uuid.uuid4()),
                "content": "Hello, I need help with data analysis",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )

    @pytest.fixture
    def concierge_state(self):
        """Create a state configured for concierge conversations."""
        state = create_unified_state(
            user_id="test_user_123",
            conversation_id="test_conv_456",
            workflow_type="chat_message",
            initial_message={
                "id": str(uuid.uuid4()),
                "content": "Hi there! What can you help me with?",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )
        state["selected_agent"] = "concierge"
        state["current_persona"] = "concierge"
        state["conversation_mode"] = ConversationMode.CONVERSATION
        return state

    def test_concierge_template_conversation_config(self):
        """Test that the upgraded concierge template supports conversations."""
        template_selector = WorkflowTemplateSelector()
        
        # Load the concierge template
        concierge_template = None
        for template_id, template in template_selector.template_cache.items():
            if template_id == "concierge_workflow":
                concierge_template = template
                break
        
        assert concierge_template is not None, "Concierge template should be loaded"
        
        # Verify conversation-friendly configuration
        workflow_config = concierge_template.get("workflow_config", {})
        
        # Check that it allows multiple executions for conversations
        assert workflow_config.get("max_agent_executions", 1) >= 2, "Should allow multiple agent executions for conversations"
        
        # Check that it doesn't auto-terminate after first response
        assert not workflow_config.get("auto_terminate_on_response", True), "Should not auto-terminate for conversations"
        
        # Check that conversation mode is enabled
        assert workflow_config.get("conversation_mode", False), "Should have conversation mode enabled"
        
        # Check execution steps allow for conversation turns
        assert workflow_config.get("max_execution_steps", 2) >= 5, "Should allow multiple execution steps for conversations"

    @pytest.mark.asyncio
    async def test_conversation_workflow_selection(self, sample_conversation_state):
        """Test that conversation workflows are properly selected."""
        template_selector = WorkflowTemplateSelector()
        
        # Test conversation message selection
        context = {
            "workflow_type": "chat_message",
            "selected_agent": "concierge",
            "conversation_mode": "conversation",
            "message": "Hello, I need help"
        }
        
        # This should select a conversation-capable template
        template_id, confidence = template_selector.select_template(
            agent_type="concierge",
            state=sample_conversation_state,
            context=context
        )
        
        assert template_id in ["concierge_workflow", "conversation_workflow"], f"Should select conversation template, got {template_id}"
        assert confidence > 0.5, f"Should have reasonable confidence, got {confidence}"

    @pytest.mark.asyncio
    async def test_conversation_state_management(self, concierge_state):
        """Test that conversation state is properly managed."""
        # Verify initial state setup
        assert concierge_state["selected_agent"] == "concierge"
        assert concierge_state["conversation_mode"] == ConversationMode.CONVERSATION
        assert len(concierge_state["messages"]) == 1  # Initial user message
        
        # Simulate adding an agent response
        agent_response = {
            "id": str(uuid.uuid4()),
            "content": "Hello! I'm your concierge. How can I help you today?",
            "type": "agent",
            "agent_id": "concierge",
            "timestamp": datetime.now().isoformat(),
            "metadata": {
                "agent_type": "concierge",
                "workflow_complete": False,  # Important: not complete for conversations
                "next_action": "CONTINUE"  # Continue conversation
            }
        }
        
        concierge_state["messages"].append(agent_response)
        
        # Verify conversation can continue
        assert len(concierge_state["messages"]) == 2
        assert not concierge_state.get("workflow_complete", False)
        assert concierge_state.get("conversation_mode") == ConversationMode.CONVERSATION

    @pytest.mark.asyncio
    async def test_workflow_routing_for_conversations(self, workflow_manager, concierge_state):
        """Test that workflow routing properly handles conversations."""
        # Mock the agent nodes to avoid actual agent loading
        workflow_manager.agent_nodes = {"concierge": Mock()}
        
        # Test routing decision for conversation
        with patch.object(workflow_manager, '_is_agent_id_match', return_value=True):
            # Simulate that agent hasn't responded yet
            routing_decision = workflow_manager._route_from_agent(concierge_state)
            
            # Should route to the selected agent for conversation
            assert routing_decision == "agent_concierge" or routing_decision == "END"

    @pytest.mark.asyncio
    async def test_conversation_completion_detection(self, workflow_manager, concierge_state):
        """Test that conversations only end when explicitly requested."""
        # Add an agent response that doesn't end the conversation
        agent_response = {
            "id": str(uuid.uuid4()),
            "content": "I can help you with that! What specific data analysis do you need?",
            "type": "agent",
            "agent_id": "concierge",
            "timestamp": datetime.now().isoformat(),
            "metadata": {
                "agent_type": "concierge",
                "workflow_complete": False,  # Conversation continues
                "next_action": "CONTINUE"
            }
        }
        
        concierge_state["messages"].append(agent_response)
        
        # Mock agent nodes
        workflow_manager.agent_nodes = {"concierge": Mock()}
        
        # Test that workflow doesn't terminate prematurely
        with patch.object(workflow_manager, '_is_agent_id_match', return_value=True):
            routing_decision = workflow_manager._route_from_agent(concierge_state)
            
            # Should end this turn but allow conversation to continue
            assert routing_decision == "END"
            
            # But workflow_complete should not be set
            assert not concierge_state.get("workflow_complete", False)

    @pytest.mark.asyncio
    async def test_explicit_conversation_termination(self, workflow_manager, concierge_state):
        """Test that conversations end when explicitly requested."""
        # Add an agent response that explicitly ends the conversation
        agent_response = {
            "id": str(uuid.uuid4()),
            "content": "Great! I've helped you get started. Feel free to reach out if you need more assistance!",
            "type": "agent",
            "agent_id": "concierge",
            "timestamp": datetime.now().isoformat(),
            "metadata": {
                "agent_type": "concierge",
                "workflow_complete": True,  # Explicitly end
                "next_action": "END",
                "conversation_ended": True
            }
        }
        
        concierge_state["messages"].append(agent_response)
        concierge_state["workflow_complete"] = True
        concierge_state["conversation_ended"] = True
        
        # Mock agent nodes
        workflow_manager.agent_nodes = {"concierge": Mock()}
        
        # Test that workflow terminates when explicitly requested
        with patch.object(workflow_manager, '_is_agent_id_match', return_value=True):
            routing_decision = workflow_manager._route_from_agent(concierge_state)
            
            # Should end the workflow
            assert routing_decision == "END"
            
            # And workflow should be marked complete
            assert concierge_state.get("workflow_complete", False)

    def test_conversation_template_validation(self):
        """Test that conversation templates pass validation."""
        template_selector = WorkflowTemplateSelector()
        
        # Get concierge template
        concierge_template = template_selector.template_cache.get("concierge_workflow")
        assert concierge_template is not None
        
        # Verify validation rules are conversation-friendly
        validation = concierge_template.get("validation", {})
        rules = validation.get("rules", [])
        
        # Should allow adequate execution steps and time for conversations
        execution_step_rule = next((rule for rule in rules if "max_execution_steps" in rule.get("rule", "")), None)
        if execution_step_rule:
            assert ">=" in execution_step_rule["rule"], "Should allow adequate execution steps"
        
        timeout_rule = next((rule for rule in rules if "execution_timeout" in rule.get("rule", "")), None)
        if timeout_rule:
            assert ">=" in timeout_rule["rule"], "Should allow adequate timeout for conversations"

    @pytest.mark.asyncio
    async def test_multi_turn_conversation_simulation(self, workflow_manager, concierge_state):
        """Simulate a multi-turn conversation to verify flow."""
        conversation_turns = [
            {
                "user": "Hello, I need help with data analysis",
                "expected_agent_continues": True
            },
            {
                "user": "I have a CSV file with sales data",
                "expected_agent_continues": True
            },
            {
                "user": "Thank you, that's all I need for now",
                "expected_agent_continues": False  # Should end conversation
            }
        ]
        
        # Mock agent nodes
        workflow_manager.agent_nodes = {"concierge": Mock()}
        
        for i, turn in enumerate(conversation_turns):
            # Add user message
            user_message = {
                "id": str(uuid.uuid4()),
                "content": turn["user"],
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
            concierge_state["messages"].append(user_message)
            concierge_state["current_message_id"] = user_message["id"]
            
            # Simulate agent response
            agent_response = {
                "id": str(uuid.uuid4()),
                "content": f"Response to: {turn['user']}",
                "type": "agent",
                "agent_id": "concierge",
                "timestamp": datetime.now().isoformat(),
                "in_response_to": user_message["id"],
                "metadata": {
                    "agent_type": "concierge",
                    "workflow_complete": not turn["expected_agent_continues"],
                    "next_action": "CONTINUE" if turn["expected_agent_continues"] else "END"
                }
            }
            concierge_state["messages"].append(agent_response)
            
            # Test routing decision
            with patch.object(workflow_manager, '_is_agent_id_match', return_value=True):
                routing_decision = workflow_manager._route_from_agent(concierge_state)
                
                # All turns should end (to allow new workflow for next turn)
                assert routing_decision == "END"
                
                # But only the last turn should mark workflow as complete
                if not turn["expected_agent_continues"]:
                    concierge_state["workflow_complete"] = True
                    concierge_state["conversation_ended"] = True

        # Verify final state
        assert len(concierge_state["messages"]) == 6  # 3 user + 3 agent messages
        assert concierge_state.get("workflow_complete", False)
        assert concierge_state.get("conversation_ended", False)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

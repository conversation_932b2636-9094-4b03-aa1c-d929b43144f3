"""
Test state synchronization between agent execution and workflow manager routing.

This test specifically addresses the timing issue where completion flags set by
agents are not being properly detected by the workflow manager's routing logic.
"""

import pytest
import asyncio
import uuid
from datetime import datetime
from unittest.mock import Mock, AsyncMock, patch

from ..core.workflow_manager import WorkflowManager
from ..nodes.base_agent_node import BaseAgentNode
from ..states.unified_state import create_unified_state, MessageType, add_message


class MockConciergeAgent(BaseAgentNode):
    """Mock concierge agent that sets completion flags."""
    
    def __init__(self):
        super().__init__("concierge", "concierge")
        self.response_with_completion = True
        
    async def _process_message(self, state):
        """Mock message processing that sets completion flags."""
        # Simulate agent response
        response = {
            "message": "Hello! I'm your concierge. How can I help you today?",
            "metadata": {
                "agent_type": "concierge",
                "workflow_complete": self.response_with_completion,
                "next_action": "END" if self.response_with_completion else "CONTINUE"
            },
            "workflow_complete": self.response_with_completion,
            "next_action": "END" if self.response_with_completion else "CONTINUE"
        }
        
        # Add response message to state
        response_message = {
            "id": str(uuid.uuid4()),
            "content": response["message"],
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": self.agent_id,
            "metadata": response["metadata"],
            "workflow_complete": response.get("workflow_complete"),
            "next_action": response.get("next_action")
        }
        state = add_message(state, response_message, MessageType.AGENT)
        
        return state


class TestStateSynchronization:
    """Test state synchronization between agent and workflow manager."""

    @pytest.fixture
    def workflow_manager(self):
        """Create a workflow manager for testing."""
        manager = WorkflowManager()
        # Add mock concierge agent
        manager.agent_nodes = {"concierge": MockConciergeAgent()}
        return manager

    @pytest.fixture
    def test_state(self):
        """Create a test state for concierge interaction."""
        return create_unified_state(
            user_id="test_user_123",
            conversation_id="test_conv_456",
            workflow_type="chat_message",
            initial_message={
                "id": str(uuid.uuid4()),
                "content": "Hello, I need help",
                "type": "user",
                "timestamp": datetime.now().isoformat()
            }
        )

    @pytest.mark.asyncio
    async def test_completion_flags_set_by_agent(self):
        """Test that agent properly sets completion flags in state."""
        agent = MockConciergeAgent()
        agent.response_with_completion = True
        
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="chat_message"
        )
        
        # Execute agent
        result_state = await agent.execute(state)
        
        # Verify completion flags are set
        assert result_state.get("workflow_complete") == True
        assert result_state.get("next_action") == "END"
        assert result_state.get("completion_flags_set_by") == "concierge"
        assert result_state.get("completion_flags_set_at") is not None
        
        # Verify message contains completion signals
        messages = result_state.get("messages", [])
        assert len(messages) >= 1
        
        agent_message = None
        for msg in messages:
            if msg.get("type") == MessageType.AGENT.value:
                agent_message = msg
                break
        
        assert agent_message is not None
        assert agent_message.get("metadata", {}).get("workflow_complete") == True
        assert agent_message.get("metadata", {}).get("next_action") == "END"

    @pytest.mark.asyncio
    async def test_workflow_manager_detects_completion_flags(self, workflow_manager):
        """Test that workflow manager properly detects completion flags."""
        # Create state with completion flags already set (simulating post-agent execution)
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="chat_message"
        )
        
        # Set completion flags as agent would
        state["workflow_complete"] = True
        state["next_action"] = "END"
        state["completion_flags_set_at"] = datetime.now().isoformat()
        state["completion_flags_set_by"] = "concierge"
        state["selected_agent"] = "concierge"
        
        # Add agent message with completion signals
        agent_message = {
            "id": str(uuid.uuid4()),
            "content": "I can help you with that!",
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": "concierge",
            "metadata": {
                "workflow_complete": True,
                "next_action": "END"
            }
        }
        state = add_message(state, agent_message, MessageType.AGENT)
        
        # Test routing decision
        routing_decision = workflow_manager._route_from_agent(state)
        
        # Should detect completion and route to END
        assert routing_decision == "END"

    @pytest.mark.asyncio
    async def test_timing_issue_detection(self, workflow_manager):
        """Test detection of timing issues where flags are set but not visible."""
        # Create state that simulates timing issue
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="chat_message"
        )
        
        # Simulate timing issue: flags were set but not visible in current state
        state["completion_flags_set_at"] = datetime.now().isoformat()
        state["completion_flags_set_by"] = "concierge"
        state["selected_agent"] = "concierge"
        # But workflow_complete and next_action are not set (timing issue)
        
        # Add agent message
        agent_message = {
            "id": str(uuid.uuid4()),
            "content": "I can help you with that!",
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": "concierge",
            "metadata": {
                "workflow_complete": True,
                "next_action": "END"
            }
        }
        state = add_message(state, agent_message, MessageType.AGENT)
        
        # Test routing decision - should detect timing issue and force termination
        routing_decision = workflow_manager._route_from_agent(state)
        
        # Should force termination due to timing issue detection
        assert routing_decision == "END"

    @pytest.mark.asyncio
    async def test_fallback_completion_detection(self, workflow_manager):
        """Test fallback completion detection through message metadata."""
        # Create state without state-level completion flags
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="chat_message"
        )
        
        state["selected_agent"] = "concierge"
        
        # Add agent message with completion signals in metadata
        agent_message = {
            "id": str(uuid.uuid4()),
            "content": "I can help you with that!",
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": "concierge",
            "metadata": {
                "workflow_complete": True,
                "next_action": "END"
            }
        }
        state = add_message(state, agent_message, MessageType.AGENT)
        
        # Test routing decision - should detect completion through fallback
        routing_decision = workflow_manager._route_from_agent(state)
        
        # Should detect completion through message metadata fallback
        assert routing_decision == "END"

    @pytest.mark.asyncio
    async def test_concierge_specific_termination(self, workflow_manager):
        """Test concierge-specific termination logic."""
        # Create state for concierge interaction
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="chat_message"
        )
        
        state["selected_agent"] = "concierge"
        
        # Add agent message without explicit completion signals
        agent_message = {
            "id": str(uuid.uuid4()),
            "content": "I can help you with that!",
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": "concierge",
            "metadata": {
                "agent_type": "concierge"
            }
        }
        state = add_message(state, agent_message, MessageType.AGENT)
        
        # Test routing decision - should terminate due to concierge-specific logic
        routing_decision = workflow_manager._route_from_agent(state)
        
        # Should terminate because concierge has responded
        assert routing_decision == "END"

    @pytest.mark.asyncio
    async def test_state_validation_after_agent_execution(self):
        """Test that state validation works after agent execution."""
        agent = MockConciergeAgent()
        agent.response_with_completion = True
        
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="chat_message"
        )
        
        # Execute agent
        result_state = await agent.execute(state)
        
        # Validate state integrity
        assert isinstance(result_state, dict)
        assert "messages" in result_state
        assert "workflow_complete" in result_state
        assert "next_action" in result_state
        assert "completion_flags_set_at" in result_state
        assert "completion_flags_set_by" in result_state
        
        # Validate completion flags are consistent
        assert result_state["workflow_complete"] == True
        assert result_state["next_action"] == "END"
        assert result_state["completion_flags_set_by"] == "concierge"

    @pytest.mark.asyncio
    async def test_no_infinite_loop_with_proper_termination(self, workflow_manager):
        """Test that proper termination prevents infinite loops."""
        # Simulate multiple routing calls (as would happen in infinite loop)
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conv",
            workflow_type="chat_message"
        )
        
        # Set up state as if agent has executed and set completion flags
        state["workflow_complete"] = True
        state["next_action"] = "END"
        state["completion_flags_set_at"] = datetime.now().isoformat()
        state["completion_flags_set_by"] = "concierge"
        state["selected_agent"] = "concierge"
        
        # Add agent message
        agent_message = {
            "id": str(uuid.uuid4()),
            "content": "I can help you with that!",
            "type": MessageType.AGENT.value,
            "timestamp": datetime.now().isoformat(),
            "agent_id": "concierge",
            "metadata": {
                "workflow_complete": True,
                "next_action": "END"
            }
        }
        state = add_message(state, agent_message, MessageType.AGENT)
        
        # Test multiple routing calls - all should return END
        for i in range(5):
            routing_decision = workflow_manager._route_from_agent(state)
            assert routing_decision == "END", f"Iteration {i}: Expected END, got {routing_decision}"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
